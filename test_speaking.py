#!/usr/bin/env python3
"""
测试口语练习模块
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        
        # 测试基础导入
        from agents.agent_base import AgentBase
        print("✓ AgentBase 导入成功")
        
        from agents.speaking_agent import SpeakingAgent
        print("✓ SpeakingAgent 导入成功")
        
        from api.app import app
        print("✓ Flask app 导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_speaking_agent():
    """测试口语练习代理"""
    try:
        print("\n测试 SpeakingAgent...")
        
        from agents.speaking_agent import SpeakingAgent
        
        # 创建实例
        agent = SpeakingAgent()
        print("✓ SpeakingAgent 实例创建成功")
        
        # 测试获取话题列表
        topics = agent.get_topics_list()
        print(f"✓ 话题列表获取成功，共 {len(topics)} 个话题")
        
        # 测试获取随机话题
        random_topic = agent.get_random_topic()
        print(f"✓ 随机话题: {random_topic}")
        
        return True
    except Exception as e:
        print(f"✗ SpeakingAgent 测试失败: {e}")
        return False

def test_api_routes():
    """测试API路由"""
    try:
        print("\n测试 API 路由...")
        
        from api.app import app
        
        with app.test_client() as client:
            # 测试健康检查
            response = client.get('/api/health')
            if response.status_code == 200:
                print("✓ 健康检查接口正常")
            else:
                print(f"✗ 健康检查失败: {response.status_code}")
                return False
            
            # 测试获取话题列表
            response = client.get('/api/speaking/topics')
            if response.status_code == 200:
                print("✓ 口语话题接口正常")
            else:
                print(f"✗ 口语话题接口失败: {response.status_code}")
                return False
        
        return True
    except Exception as e:
        print(f"✗ API 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("LanguageMentor 口语练习模块测试")
    print("=" * 50)
    
    success = True
    
    # 测试导入
    if not test_imports():
        success = False
    
    # 测试 SpeakingAgent
    if not test_speaking_agent():
        success = False
    
    # 测试 API 路由
    if not test_api_routes():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！")
        print("口语练习模块已成功集成到 LanguageMentor 项目中")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    print("=" * 50)
    
    return success

if __name__ == "__main__":
    main()
