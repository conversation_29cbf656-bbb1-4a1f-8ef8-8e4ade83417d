from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.scenario_agent import ScenarioAgent
from agents.conversation_agent import ConversationAgent
from agents.vocab_agent import VocabAgent
from agents.speaking_agent import SpeakingAgent
from utils.logger import LOG

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置日志
logging.basicConfig(level=logging.INFO)

# 全局智能体实例
agents = {
    'scenario': {},  # 场景代理将根据场景类型动态创建
    'conversation': ConversationAgent(),
    'vocab': VocabAgent(),
    'speaking': SpeakingAgent()
}

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'message': 'LanguageMentor API is running'
    })

@app.route('/api/scenario/list', methods=['GET'])
def get_scenarios():
    """获取可用场景列表"""
    scenarios = [
        {
            'id': 'job_interview',
            'name': '求职面试',
            'description': '模拟真实面试场景，提升面试技巧',
            'icon': 'fas fa-briefcase'
        },
        {
            'id': 'hotel_checkin',
            'name': '酒店入住',
            'description': '练习酒店入住对话，掌握旅行英语',
            'icon': 'fas fa-hotel'
        }
    ]
    return jsonify({
        'success': True,
        'data': scenarios
    })

@app.route('/api/scenario/<scenario_id>/intro', methods=['GET'])
def get_scenario_intro(scenario_id):
    """获取场景介绍"""
    try:
        # 获取项目根目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        intro_file = os.path.join(project_root, "content", "page", f"{scenario_id}.md")

        if os.path.exists(intro_file):
            with open(intro_file, 'r', encoding='utf-8') as f:
                intro_content = f.read()
            return jsonify({
                'success': True,
                'data': {
                    'intro': intro_content
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': f'场景介绍文件未找到: {scenario_id}'
            }), 404
    except Exception as e:
        LOG.error(f"获取场景介绍失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/scenario/<scenario_id>/start', methods=['POST'])
def start_scenario(scenario_id):
    """开始场景对话"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', f"{scenario_id}_default")
        
        # 创建或获取场景代理
        if scenario_id not in agents['scenario']:
            agents['scenario'][scenario_id] = ScenarioAgent(scenario_id, session_id)
        
        agent = agents['scenario'][scenario_id]
        initial_message = agent.start_new_session(session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': initial_message,
                'session_id': session_id
            }
        })
    except Exception as e:
        LOG.error(f"开始场景对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/scenario/<scenario_id>/chat', methods=['POST'])
def scenario_chat(scenario_id):
    """场景对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', f"{scenario_id}_default")
        
        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400
        
        # 获取或创建场景代理
        if scenario_id not in agents['scenario']:
            agents['scenario'][scenario_id] = ScenarioAgent(scenario_id, session_id)
        
        agent = agents['scenario'][scenario_id]
        bot_response = agent.chat_with_history(user_input, session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': bot_response
            }
        })
    except Exception as e:
        LOG.error(f"场景对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/conversation/chat', methods=['POST'])
def conversation_chat():
    """自由对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', 'conversation_default')
        
        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400
        
        agent = agents['conversation']
        bot_response = agent.chat_with_history(user_input, session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': bot_response
            }
        })
    except Exception as e:
        LOG.error(f"自由对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/intro', methods=['GET'])
def get_vocab_intro():
    """获取词汇学习介绍"""
    try:
        # 获取项目根目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        intro_file = os.path.join(project_root, "content", "page", "vocab_study.md")

        if os.path.exists(intro_file):
            with open(intro_file, 'r', encoding='utf-8') as f:
                intro_content = f.read()
            return jsonify({
                'success': True,
                'data': {
                    'intro': intro_content
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': '词汇学习介绍文件未找到'
            }), 404
    except Exception as e:
        LOG.error(f"获取词汇学习介绍失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/start', methods=['POST'])
def start_vocab():
    """开始词汇学习（下一关）"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', 'vocab_default')
        
        agent = agents['vocab']
        agent.restart_session(session_id)
        
        # 发送初始消息
        initial_input = "Let's do it"
        bot_response = agent.chat_with_history(initial_input, session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'user_message': initial_input,
                'bot_message': bot_response,
                'session_id': session_id
            }
        })
    except Exception as e:
        LOG.error(f"开始词汇学习失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/chat', methods=['POST'])
def vocab_chat():
    """词汇学习对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', 'vocab_default')
        
        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400
        
        agent = agents['vocab']
        bot_response = agent.chat_with_history(user_input, session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': bot_response
            }
        })
    except Exception as e:
        LOG.error(f"词汇学习对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config', methods=['POST'])
def update_config():
    """更新配置（API Key等）"""
    try:
        data = request.get_json()
        api_key = data.get('api_key')
        api_base = data.get('api_base')
        
        if api_key:
            os.environ['OPENAI_API_KEY'] = api_key
        if api_base:
            os.environ['OPENAI_API_BASE'] = api_base
        
        # 重新初始化智能体以使用新配置
        agents['conversation'] = ConversationAgent()
        agents['vocab'] = VocabAgent()
        agents['speaking'] = SpeakingAgent()
        agents['scenario'] = {}  # 清空场景代理，将在下次使用时重新创建

        return jsonify({
            'success': True,
            'message': '配置更新成功'
        })
    except Exception as e:
        LOG.error(f"更新配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ==================== 口语练习相关接口 ====================

@app.route('/api/speaking/topics', methods=['GET'])
def get_speaking_topics():
    """获取口语练习话题列表"""
    try:
        topics = agents['speaking'].get_topics_list()
        return jsonify({
            'success': True,
            'topics': topics
        })
    except Exception as e:
        LOG.error(f"获取话题列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/speaking/start', methods=['POST'])
def start_speaking_session():
    """开始新的口语练习会话"""
    try:
        data = request.get_json()
        topic = data.get('topic')  # 可选，如果不提供则随机选择
        difficulty = data.get('difficulty', 'intermediate')
        session_id = data.get('session_id', 'default_speaking')

        # 创建新的口语练习代理实例
        speaking_agent = SpeakingAgent(session_id=session_id)
        agents['speaking'] = speaking_agent

        # 开始新会话
        initial_message = speaking_agent.start_new_session(topic=topic, difficulty=difficulty)

        return jsonify({
            'success': True,
            'message': initial_message,
            'session_id': session_id,
            'topic': topic,
            'difficulty': difficulty
        })

    except Exception as e:
        LOG.error(f"开始口语练习会话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/speaking/chat', methods=['POST'])
def speaking_chat():
    """处理口语练习对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '').strip()
        session_id = data.get('session_id', 'default_speaking')

        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息内容不能为空'
            }), 400

        # 确保使用正确的会话ID
        if agents['speaking'].session_id != session_id:
            agents['speaking'] = SpeakingAgent(session_id=session_id)

        # 获取AI回复和反馈
        response = agents['speaking'].get_speaking_feedback(user_input)

        return jsonify({
            'success': True,
            'response': response,
            'session_id': session_id
        })

    except Exception as e:
        LOG.error(f"口语练习对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/speaking/random-topic', methods=['GET'])
def get_random_speaking_topic():
    """获取随机口语练习话题"""
    try:
        topic = agents['speaking'].get_random_topic()
        return jsonify({
            'success': True,
            'topic': topic
        })
    except Exception as e:
        LOG.error(f"获取随机话题失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
