from langchain_core.messages import AIMessage
from .session_history import get_session_history
from .agent_base import AgentBase
from utils.logger import LOG
import random

class SpeakingAgent(AgentBase):
    """
    口语练习代理类，专门用于AI口语对话练习。
    继承自 AgentBase 基类。
    """
    
    def __init__(self, session_id=None):
        # 调用父类的构造函数，初始化代理名称、提示文件路径以及可选的会话 ID
        super().__init__(
            name="speaking_practice",  # 定义代理的名称
            prompt_file="prompts/speaking_prompt.txt",  # 提示词文件的路径
            session_id=session_id  # 会话唯一标识符，默认为 None
        )
        
        # 口语练习的话题列表
        self.topics = [
            "自我介绍和个人爱好",
            "日常生活和工作",
            "旅行和文化体验", 
            "食物和烹饪",
            "电影和娱乐",
            "科技和未来",
            "环境保护",
            "健康和运动",
            "教育和学习",
            "家庭和朋友关系"
        ]
        
        # 练习难度级别
        self.difficulty_levels = {
            "beginner": "初级 - 使用简单词汇和句型",
            "intermediate": "中级 - 使用常见词汇和复杂句型", 
            "advanced": "高级 - 使用高级词汇和复杂表达"
        }

    def start_new_session(self, topic=None, difficulty="intermediate"):
        """
        开始新的口语练习会话
        
        参数:
            topic (str, optional): 练习话题，如果未提供则随机选择
            difficulty (str): 难度级别 (beginner/intermediate/advanced)
            
        返回:
            str: AI的开场白
        """
        # 清除会话历史
        history = get_session_history(self.session_id)
        history.clear()
        
        # 选择话题
        if topic is None:
            topic = random.choice(self.topics)
        
        # 构建开场白
        opening_messages = [
            f"Hi! I'm your English speaking partner. Today let's practice speaking about '{topic}'. I'll help you improve your pronunciation, fluency, and confidence. Feel free to speak naturally - I'm here to support you!",
            f"Hello! Ready for some speaking practice? Our topic today is '{topic}'. Don't worry about making mistakes - that's how we learn! Let's start with a simple question.",
            f"Welcome to our speaking session! We'll be discussing '{topic}' today. Remember, the goal is to practice speaking, so take your time and express yourself freely."
        ]
        
        opening = random.choice(opening_messages)
        
        # 添加话题相关的开场问题
        topic_questions = {
            "自我介绍和个人爱好": "Could you start by telling me a bit about yourself and what you enjoy doing in your free time?",
            "日常生活和工作": "What does a typical day look like for you? I'd love to hear about your daily routine.",
            "旅行和文化体验": "Have you traveled anywhere interesting recently, or is there a place you'd love to visit?",
            "食物和烹饪": "What's your favorite type of food? Do you enjoy cooking, or do you prefer eating out?",
            "电影和娱乐": "What kind of movies or TV shows do you enjoy? Have you watched anything good lately?",
            "科技和未来": "How do you think technology has changed our daily lives? What excites you about future innovations?",
            "环境保护": "What do you think are the most important environmental issues we face today?",
            "健康和运动": "How do you stay healthy and active? Do you have any favorite sports or exercises?",
            "教育和学习": "What's the most interesting thing you've learned recently? How do you prefer to learn new things?",
            "家庭和朋友关系": "Tell me about your family or close friends. What makes these relationships special to you?"
        }
        
        question = topic_questions.get(topic, "What would you like to share about this topic?")
        
        full_message = f"{opening}\n\n{question}"
        
        LOG.info(f"[SpeakingAgent] 开始新会话 - 话题: {topic}, 难度: {difficulty}")
        return full_message

    def get_speaking_feedback(self, user_input):
        """
        为用户的口语练习提供反馈
        
        参数:
            user_input (str): 用户的输入文本
            
        返回:
            str: AI的反馈和建议
        """
        # 在用户输入前添加反馈指令
        feedback_prompt = f"""
        Please provide helpful feedback on the following response and continue the conversation naturally:
        
        User's response: "{user_input}"
        
        In your response, please:
        1. Acknowledge what they said positively
        2. If there are any grammar or vocabulary improvements, mention them gently
        3. Ask a follow-up question to keep the conversation flowing
        4. Keep the tone encouraging and supportive
        """
        
        response = self.chat_with_history(feedback_prompt)
        LOG.info(f"[SpeakingAgent] 提供反馈: {response}")
        return response

    def get_random_topic(self):
        """
        获取随机话题
        
        返回:
            str: 随机选择的话题
        """
        return random.choice(self.topics)

    def get_topics_list(self):
        """
        获取所有可用话题列表
        
        返回:
            list: 话题列表
        """
        return self.topics.copy()

    def restart_session(self, session_id=None):
        """
        重新启动会话，清除会话历史
        
        参数:
            session_id (str, optional): 会话的唯一标识符
            
        返回:
            object: 清空后的会话历史
        """
        if session_id is None:
            session_id = self.session_id
            
        history = get_session_history(session_id)
        history.clear()
        LOG.debug(f"[SpeakingAgent][{session_id}] 会话历史已清除")
        return history
