import os
import sys
import threading
import time
import webbrowser
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from api.app import app
from utils.logger import LOG

def start_api_server():
    """启动API服务器"""
    try:
        print("正在启动 LanguageMentor API 服务器...")
        LOG.info("正在启动 LanguageMentor API 服务器...")
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e:
        print(f"API服务器启动失败: {e}")
        LOG.error(f"API服务器启动失败: {e}")
        sys.exit(1)

def start_web_server():
    """启动Web服务器（简单的HTTP服务器）"""
    try:
        import http.server
        import socketserver
        
        web_dir = project_root / "web"
        os.chdir(web_dir)
        
        PORT = 8000
        Handler = http.server.SimpleHTTPRequestHandler
        
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            LOG.info(f"正在启动 Web 服务器，端口: {PORT}")
            LOG.info(f"Web 界面地址: http://localhost:{PORT}")
            httpd.serve_forever()
            
    except Exception as e:
        LOG.error(f"Web服务器启动失败: {e}")
        sys.exit(1)

def open_browser():
    """延迟打开浏览器"""
    time.sleep(3)  # 等待服务器启动
    try:
        webbrowser.open('http://localhost:8000')
        LOG.info("已在浏览器中打开 LanguageMentor")
    except Exception as e:
        LOG.warning(f"无法自动打开浏览器: {e}")
        LOG.info("请手动访问: http://localhost:8000")

def check_dependencies():
    """检查依赖项"""
    try:
        LOG.info("依赖项检查通过")
        return True
    except ImportError as e:
        LOG.error(f"缺少依赖项: {e}")
        LOG.error("请运行: pip install flask flask-cors python-dotenv")
        return False

def check_environment():
    """检查环境配置"""
    from dotenv import load_dotenv, find_dotenv
    
    # 加载环境变量
    env_file = find_dotenv()
    if env_file:
        load_dotenv(env_file)
        LOG.info(f"已加载环境配置文件: {env_file}")
    else:
        LOG.warning("未找到 .env 文件，请确保已配置千问 API")
    
    # 检查必要的目录和文件
    required_dirs = ['agents', 'prompts', 'content', 'web', 'api']
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            LOG.error(f"缺少必要目录: {dir_name}")
            return False
    
    required_files = [
        'web/index.html',
        'web/js/main.js',
        'web/js/api.js',
        'web/js/components.js',
        'web/css/main.css',
        'web/css/components.css',
        'api/app.py'
    ]
    
    for file_path in required_files:
        full_path = project_root / file_path
        if not full_path.exists():
            LOG.error(f"缺少必要文件: {file_path}")
            return False
    
    LOG.info("环境检查通过")
    return True

def print_startup_info():
    """打印启动信息"""
    print("\n" + "="*60)
    print("🎓 LanguageMentor 英语私教 - HTML版本")
    print("="*60)
    print("  功能特性:")
    print("  • 场景对话练习 (求职面试、酒店入住)")
    print("  • 自由英语对话")
    print("  • 闯关式单词学习")
    print("  • 响应式Web界面")
    print("  • 保留原有智能体核心功能")
    print("\n 访问地址:")
    print("  • Web界面: http://localhost:8000")
    print("  • API服务: http://localhost:5000")
    print("\n 配置说明:")
    print("  • 在Web界面的设置中配置千问 API Key")
    print("  • 或在项目根目录创建.env文件配置环境变量")
    print("="*60)
    print("正在启动服务...")

def main():
    """主函数"""
    print_startup_info()

    # 检查依赖项
    print("检查依赖项...")
    if not check_dependencies():
        sys.exit(1)

    # 检查环境
    print("检查环境...")
    if not check_environment():
        sys.exit(1)

    try:
        print("启动服务...")
        # 启动API服务器（后台线程）
        api_thread = threading.Thread(target=start_api_server, daemon=True)
        api_thread.start()
        print("API线程已启动")

        # 延迟打开浏览器（后台线程）
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        print("浏览器线程已启动")

        # 启动Web服务器（主线程）
        print("启动Web服务器...")
        start_web_server()

    except KeyboardInterrupt:
        LOG.info("收到中断信号，正在关闭服务...")
        print("\n👋 感谢使用 LanguageMentor！")
        sys.exit(0)
    except Exception as e:
        print(f"启动失败: {e}")
        LOG.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
