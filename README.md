# LanguageMentor 英语私教

## 📋 项目概述

	LanguageMentor 是一款基于大模型的在线英语私教系统，提供英语对话练习和场景化学习训练。
    用户可以选择不同的场景，或直接与对话代理人进行自由对话，模拟真实生活中的英语交流场景，提升语言能力。

## 🏗️ 产品设计

- 核心功能：
  - 基础教学：涵盖词汇积累、语法学习、阅读理解和写作技巧等基础内容。
  - 对话式训练：模拟真实场景的对话练习，提升学生的口语表达能力和听力理解能力。
- 用户学习路径：
  - 初学者：注重词汇和基础语法的学习，通过简单对话练习提高自信心。
  - 中级学员：结合复杂语法和高级词汇，进行更深入的阅读和写作训练。
  - 高级学员：重点练习口语和听力，通过模拟真实场景的对话提升实战能力。
- 课程设计：
  - 词汇积累：采用词根词缀法和常用词汇表，帮助学生高效记忆单词。
  - 语法学习：通过系统的语法讲解和练习，夯实学生的语法基础。
  - 阅读理解：提供不同难度的阅读材料，训练学生的阅读速度和理解能力。
  - 写作技巧：指导学生如何进行段落和文章的结构化写作。

## 🚀 快速开始
以下是快速开始使用 LanguageMentor 的步骤：

1. **进入项目目录**
   ```bash
   cd LanguageMentor
   ```

2. **创建 Python 虚拟环境**
   使用 miniconda 或类似 Python 虚拟环境管理工具，创建一个项目专属的环境，取名为`lm`：
   ```bash
   conda create -n LanguageMentor python=3.10
   ```
   激活虚拟环境
   ```bash
   conda activate LanguageMentor
   ```

3. **配置开发环境**
   然后运行以下命令安装所需依赖：
   ```bash
   pip install -r requirements.txt
   ```

   根据需要配置你的环境变量，例如 `OpenAI_API_KEY` 等。

4. **运行应用**
   启动应用程序：
   ```bash
   python main.py
   ```
   


# 🏗️ 核心架构

### 1. 项目结构 (HTML版本)

```
LanguageMentor/
│── web/                 # HTML前端
│   ├── index.html       # 主页面
│   ├── css/            # 样式文件
│   │   ├── main.css    # 主样式
│   │   └── components.css # 组件样式
│   ├── js/             # JavaScript文件
│   │   ├── main.js     # 主逻辑
│   │   ├── api.js      # API调用
│   │   └── components.js # UI组件
│   └── assets/         # 静态资源
│── api/                # 后端API服务
│   ├── app.py          # Flask/FastAPI应用
│   ├── routes/         # API路由
│   │   ├── scenario.py # 场景API
│   │   ├── conversation.py # 对话API
│   │   └── vocab.py    # 词汇API
│   └── models/         # 数据模型
│── agents/             # 智能代理模块 (保留)
│   ├── agent_base.py   # 代理基类
│   ├── scenario_agent.py # 场景对话代理
│   ├── conversation_agent.py # 自由对话代理
│   ├── vocab_agent.py  # 词汇学习代理
│   └── session_history.py # 会话历史管理
│── utils/              # 工具模块
│   └── logger.py       # 日志管理
├── content/            # 内容资源
│   ├── intro/          # 场景初始对话
│   └── page/           # 页面介绍文档
└── prompts/            # 提示词模板
```

### 2. 核心组件

#### 2.1 配置 OpenAI
- **功能**: OpenAI API 密钥和基础 URL配置
- **配置项**:
  - API 密钥 ： OPENAI_API_KEY
  - 基础 URL ： OPENAI_API_BASE

#### 2.2 智能体Agnet (agents/)

##### 2.2.1 Agent基类 (AgentBase)
- **设计模式**: 抽象基类模式
- **核心功能**:
  - 加载系统提示词和初始消息
  - 初始化 ChatOpenAI 模型
  - 管理对话历史记录
  - 提供统一的对话接口

##### 2.2.2 场景Agent (ScenarioAgent)
- **继承**: AgentBase
- **特色功能**: 
  - 支持多种预定义场景（求职面试、酒店入住等）
  - 随机选择初始 AI 消息
  - 场景特定的提示词和对话流程

##### 2.2.3 对话Agent (ConversationAgent)
- **继承**: AgentBase
- **功能**: 提供自由形式的英语对话练习
- **特点**: 简洁的实现，专注于通用对话能力

##### 2.2.4 词汇Agent (VocabAgent)
- **继承**: AgentBase
- **特色功能**:
  - 支持会话重启（清除历史）
  - 闯关式词汇学习体验
  - 个性化词汇学习路径

#### 2.3 会话历史管理 (session_history.py)
- **存储方式**: 内存存储 (InMemoryChatMessageHistory)
- **管理策略**: 基于 session_id 的会话隔离
- **核心函数**: `get_session_history()`

#### 2.4 用户界面系统 (tabs/)

##### 2.4.1 场景标签页 (scenario_tab.py)
- **组件**: Radio 选择器 + ChatInterface
- **功能**: 
  - 场景选择和介绍展示
  - 场景特定的对话界面
  - 动态加载场景描述

##### 2.4.2 对话标签页 (conversation_tab.py)
- **组件**: ChatInterface
- **功能**: 自由英语对话练习

##### 2.4.3 词汇标签页 (vocab_tab.py)
- **组件**: ChatInterface + 重启按钮
- **功能**: 
  - 闯关式词汇学习
  - 支持学习进度重置
  - 动态加载学习内容

#### 2.5 日志 (logger.py)
- **日志库**: Loguru
- **输出目标**: 控制台 + 文件 (logs/app.log)
- **日志级别**: DEBUG, INFO, ERROR
- **特性**: 彩色输出、自动轮换

## 🔄 数据流程

### 1. 应用启动流程
```
启动脚本  → 创建 Gradio 应用 → 初始化代理 → 启动服务
```

### 2. 对话处理流程
```
用户输入 → UI 组件 → 对应代理 → LangChain 处理 → OpenAI API → 响应返回 → UI 更新
```

### 3. 会话管理流程
```
会话 ID → 历史管理器 → 内存存储 → 上下文维护 → 对话连续性
```

## 🎯 核心特性

### 1. 模块化设计
- **代理模式**: 每个功能模块独立的代理类
- **标签页分离**: UI 组件按功能模块组织


### 2. 可扩展性
- **新场景添加**: 只需添加提示词文件和初始消息
- **新代理类型**: 继承 AgentBase 即可快速实现
- **UI 扩展**: 标签页模式便于添加新功能

### 3. 用户体验
- **多样化学习**: 场景、对话、词汇三种学习模式
- **会话持久**: 对话历史在会话期间保持
- **即时反馈**: 实时的 AI 对话响应

### 4. 技术栈
- **前端**: Gradio (Python Web UI 框架)
- **后端**: LangChain + OpenAI API
- **日志**: Loguru

## 📝 开发指南

### 添加新场景
1. 在 `prompts/` 目录添加场景提示词文件
2. 在 `content/intro/` 添加初始对话 JSON 文件
3. 在 `content/page/` 添加场景介绍 Markdown 文件
4. 在 `scenario_tab.py` 中注册新场景

### 添加新Agent
1. 继承 `AgentBase` 类
2. 实现特定的业务逻辑
3. 创建对应的 UI 标签页
4. 在主应用中注册

这个架构设计确保了代码的可维护性、可扩展性和用户体验的一致性。