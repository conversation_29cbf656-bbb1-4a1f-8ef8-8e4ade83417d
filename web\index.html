<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LanguageMentor 英语私教</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-graduation-cap"></i>
                    LanguageMentor 英语私教
                </h1>
                <div class="header-actions">
                    <button class="btn btn-outline" id="settingsBtn">
                        <i class="fas fa-cog"></i>
                        设置
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 标签页导航 -->
            <nav class="tab-nav">
                <button class="tab-btn active" data-tab="scenario">
                    <i class="fas fa-theater-masks"></i>
                    场景练习
                </button>
                <button class="tab-btn" data-tab="conversation">
                    <i class="fas fa-comments"></i>
                    自由对话
                </button>
                <button class="tab-btn" data-tab="vocab">
                    <i class="fas fa-book"></i>
                    单词学习
                </button>
            </nav>

            <!-- 场景练习标签页 -->
            <div class="tab-content active" id="scenario-tab">
                <div class="tab-header">
                    <h2>选择一个场景完成目标和挑战</h2>
                </div>
                
                <div class="scenario-selector">
                    <div class="scenario-options">
                        <label class="scenario-option">
                            <input type="radio" name="scenario" value="job_interview">
                            <div class="scenario-card">
                                <i class="fas fa-briefcase"></i>
                                <h3>求职面试</h3>
                                <p>模拟真实面试场景，提升面试技巧</p>
                            </div>
                        </label>
                        <label class="scenario-option">
                            <input type="radio" name="scenario" value="hotel_checkin">
                            <div class="scenario-card">
                                <i class="fas fa-hotel"></i>
                                <h3>酒店入住</h3>
                                <p>练习酒店入住对话，掌握旅行英语</p>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="scenario-intro" id="scenarioIntro">
                    <p>请选择一个场景开始练习</p>
                </div>

                <div class="chat-container" id="scenarioChat">
                    <div class="chat-messages" id="scenarioMessages">
                        <div class="welcome-message">
                            <strong>你的英语私教 RandyXu</strong><br><br>
                            选择场景后开始对话吧！
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <input type="text" class="chat-input" id="scenarioInput" placeholder="输入你的回复..." disabled>
                            <button class="send-btn" id="scenarioSendBtn" disabled>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 自由对话标签页 -->
            <div class="tab-content" id="conversation-tab">
                <div class="tab-header">
                    <h2>练习英语对话</h2>
                </div>

                <div class="chat-container" id="conversationChat">
                    <div class="chat-messages" id="conversationMessages">
                        <div class="welcome-message">
                            <strong>你的英语私教 RandyXu</strong><br><br>
                            想和我聊什么话题都可以，记得用英语哦！
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <input type="text" class="chat-input" id="conversationInput" placeholder="输入你的消息...">
                            <button class="send-btn" id="conversationSendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 单词学习标签页 -->
            <div class="tab-content" id="vocab-tab">
                <div class="tab-header">
                    <h2>闯关背单词</h2>
                    <button class="btn btn-primary" id="nextRoundBtn">
                        <i class="fas fa-forward"></i>
                        下一关
                    </button>
                </div>

                <div class="vocab-intro" id="vocabIntro">
                    <!-- 词汇学习介绍将在这里加载 -->
                </div>

                <div class="chat-container" id="vocabChat">
                    <div class="chat-messages" id="vocabMessages">
                        <div class="welcome-message">
                            <strong>你的英语私教 RandyXu</strong><br><br>
                            开始学习新单词吧！
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <div class="chat-input-wrapper">
                            <input type="text" class="chat-input" id="vocabInput" placeholder="输入你的回答...">
                            <button class="send-btn" id="vocabSendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 设置模态框 -->
        <div class="modal" id="settingsModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>设置</h3>
                    <button class="modal-close" id="closeSettingsBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label for="apiKey">Qwen API Key:</label>
                        <input type="password" id="apiKey" placeholder="输入你的千问 API Key">
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancelSettingsBtn">取消</button>
                    <button class="btn btn-primary" id="saveSettingsBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在处理...</p>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
