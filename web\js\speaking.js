// 口语练习模块
class SpeakingPractice {
    constructor() {
        this.currentTopic = null;
        this.sessionId = 'speaking_' + Date.now();
        this.isRecording = false;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.currentUtterance = null;
        
        this.initSpeechRecognition();
        this.initEventListeners();
    }

    initSpeechRecognition() {
        // 检查浏览器是否支持语音识别
        if ('webkitSpeechRecognition' in window) {
            this.recognition = new webkitSpeechRecognition();
        } else if ('SpeechRecognition' in window) {
            this.recognition = new SpeechRecognition();
        } else {
            console.warn('浏览器不支持语音识别');
            return;
        }

        if (this.recognition) {
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'en-US';

            this.recognition.onstart = () => {
                this.onRecordingStart();
            };

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.onRecognitionResult(transcript);
            };

            this.recognition.onerror = (event) => {
                this.onRecognitionError(event.error);
            };

            this.recognition.onend = () => {
                this.onRecordingEnd();
            };
        }
    }

    initEventListeners() {
        // 话题选择按钮
        const topicSelectBtn = document.getElementById('topicSelectBtn');
        if (topicSelectBtn) {
            topicSelectBtn.addEventListener('click', () => {
                this.toggleTopicSelector();
            });
        }

        // 随机话题按钮
        const newTopicBtn = document.getElementById('newTopicBtn');
        if (newTopicBtn) {
            newTopicBtn.addEventListener('click', () => {
                this.getRandomTopic();
            });
        }

        // 录音按钮
        const recordBtn = document.getElementById('recordBtn');
        if (recordBtn) {
            recordBtn.addEventListener('click', () => {
                this.toggleRecording();
            });
        }

        // 播放按钮
        const playBtn = document.getElementById('playBtn');
        if (playBtn) {
            playBtn.addEventListener('click', () => {
                this.playLastResponse();
            });
        }

        // 发送按钮
        const sendBtn = document.getElementById('speakingSendBtn');
        if (sendBtn) {
            sendBtn.addEventListener('click', () => {
                this.sendMessage();
            });
        }

        // 输入框回车发送
        const input = document.getElementById('speakingInput');
        if (input) {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendMessage();
                }
            });
        }
    }

    async loadTopics() {
        try {
            const response = await api.getSpeakingTopics();
            if (response.success) {
                this.displayTopics(response.topics);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('加载话题失败:', error);
            ErrorHandler.show('加载话题失败: ' + error.message);
        }
    }

    displayTopics(topics) {
        const topicGrid = document.getElementById('topicGrid');
        if (!topicGrid) return;

        topicGrid.innerHTML = '';
        
        topics.forEach(topic => {
            const topicCard = document.createElement('div');
            topicCard.className = 'topic-card';
            topicCard.textContent = topic;
            topicCard.addEventListener('click', () => {
                this.selectTopic(topic);
            });
            topicGrid.appendChild(topicCard);
        });
    }

    toggleTopicSelector() {
        const selector = document.getElementById('topicSelector');
        if (!selector) return;

        if (selector.style.display === 'none') {
            this.loadTopics();
            selector.style.display = 'block';
        } else {
            selector.style.display = 'none';
        }
    }

    async getRandomTopic() {
        try {
            const response = await api.getRandomSpeakingTopic();
            if (response.success) {
                this.selectTopic(response.topic);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('获取随机话题失败:', error);
            ErrorHandler.show('获取随机话题失败: ' + error.message);
        }
    }

    async selectTopic(topic) {
        try {
            LoadingManager.show('开始口语练习...');
            
            const response = await api.startSpeakingSession({
                topic: topic,
                difficulty: 'intermediate',
                session_id: this.sessionId
            });

            if (response.success) {
                this.currentTopic = topic;
                this.displayCurrentTopic(topic);
                this.hideTopicSelector();
                this.clearMessages();
                this.addMessage(response.message, 'ai');
                this.lastAIResponse = response.message;
                this.showPlayButton();
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('选择话题失败:', error);
            ErrorHandler.show('选择话题失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    displayCurrentTopic(topic) {
        const currentTopic = document.getElementById('currentTopic');
        const topicName = document.getElementById('currentTopicName');
        
        if (currentTopic && topicName) {
            topicName.textContent = topic;
            currentTopic.style.display = 'block';
        }
    }

    hideTopicSelector() {
        const selector = document.getElementById('topicSelector');
        if (selector) {
            selector.style.display = 'none';
        }
    }

    toggleRecording() {
        if (!this.recognition) {
            ErrorHandler.show('您的浏览器不支持语音识别功能');
            return;
        }

        if (this.isRecording) {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    startRecording() {
        try {
            this.recognition.start();
        } catch (error) {
            console.error('开始录音失败:', error);
            ErrorHandler.show('开始录音失败: ' + error.message);
        }
    }

    stopRecording() {
        if (this.recognition) {
            this.recognition.stop();
        }
    }

    onRecordingStart() {
        this.isRecording = true;
        const recordBtn = document.getElementById('recordBtn');
        const voiceStatus = document.getElementById('voiceStatus');
        
        if (recordBtn) {
            recordBtn.classList.add('recording');
            recordBtn.innerHTML = '<i class="fas fa-stop"></i>';
            recordBtn.title = '点击停止录音';
        }
        
        if (voiceStatus) {
            voiceStatus.style.display = 'block';
        }
    }

    onRecordingEnd() {
        this.isRecording = false;
        const recordBtn = document.getElementById('recordBtn');
        const voiceStatus = document.getElementById('voiceStatus');
        
        if (recordBtn) {
            recordBtn.classList.remove('recording');
            recordBtn.innerHTML = '<i class="fas fa-microphone"></i>';
            recordBtn.title = '点击开始录音';
        }
        
        if (voiceStatus) {
            voiceStatus.style.display = 'none';
        }
    }

    onRecognitionResult(transcript) {
        const input = document.getElementById('speakingInput');
        if (input) {
            input.value = transcript;
        }
        
        // 自动发送识别到的文本
        this.sendMessage();
    }

    onRecognitionError(error) {
        console.error('语音识别错误:', error);
        let errorMessage = '语音识别失败';
        
        switch (error) {
            case 'no-speech':
                errorMessage = '没有检测到语音，请重试';
                break;
            case 'audio-capture':
                errorMessage = '无法访问麦克风，请检查权限';
                break;
            case 'not-allowed':
                errorMessage = '麦克风权限被拒绝，请在浏览器设置中允许';
                break;
            default:
                errorMessage = `语音识别错误: ${error}`;
        }
        
        ErrorHandler.show(errorMessage);
    }

    async sendMessage() {
        const input = document.getElementById('speakingInput');
        if (!input) return;

        const message = input.value.trim();
        if (!message) return;

        if (!this.currentTopic) {
            ErrorHandler.show('请先选择一个话题开始练习');
            return;
        }

        try {
            // 显示用户消息
            this.addMessage(message, 'user');
            input.value = '';
            
            LoadingManager.show('AI正在回复...');

            const response = await api.speakingChat({
                message: message,
                session_id: this.sessionId
            });

            if (response.success) {
                this.addMessage(response.response, 'ai');
                this.lastAIResponse = response.response;
                this.showPlayButton();
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    addMessage(content, type) {
        const messagesContainer = document.getElementById('speakingMessages');
        if (!messagesContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = content;
        
        messageDiv.appendChild(messageContent);
        messagesContainer.appendChild(messageDiv);
        
        // 滚动到底部
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    clearMessages() {
        const messagesContainer = document.getElementById('speakingMessages');
        if (messagesContainer) {
            messagesContainer.innerHTML = '';
        }
    }

    showPlayButton() {
        const playBtn = document.getElementById('playBtn');
        if (playBtn) {
            playBtn.style.display = 'block';
        }
    }

    playLastResponse() {
        if (!this.lastAIResponse) return;

        // 停止当前播放
        if (this.currentUtterance) {
            this.synthesis.cancel();
        }

        // 创建新的语音合成
        this.currentUtterance = new SpeechSynthesisUtterance(this.lastAIResponse);
        this.currentUtterance.lang = 'en-US';
        this.currentUtterance.rate = 0.9;
        this.currentUtterance.pitch = 1;

        const playBtn = document.getElementById('playBtn');
        
        this.currentUtterance.onstart = () => {
            if (playBtn) {
                playBtn.classList.add('playing');
                playBtn.innerHTML = '<i class="fas fa-stop"></i>';
                playBtn.title = '停止播放';
            }
        };

        this.currentUtterance.onend = () => {
            if (playBtn) {
                playBtn.classList.remove('playing');
                playBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                playBtn.title = '播放AI回复';
            }
        };

        this.synthesis.speak(this.currentUtterance);
    }
}

// 导出到全局
window.SpeakingPractice = SpeakingPractice;
