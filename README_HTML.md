# LanguageMentor HTML版本

🎓 **英语私教智能体** - 保留核心功能，全新HTML界面

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements_html.txt
```

### 2. 配置环境（可选）
创建 `.env` 文件：
```env
OPENAI_API_KEY=your_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
```

### 3. 启动应用
```bash
python main_html.py
```

### 4. 访问界面
- 🌐 Web界面: http://localhost:8000
- 🔧 API服务: http://localhost:5000

## ✨ 功能特性

### 🎭 场景对话练习
- **求职面试**: 模拟真实面试场景
- **酒店入住**: 练习旅行英语对话
- 智能对话引导和反馈

### 💬 自由英语对话
- 开放式英语对话练习
- 个性化学习建议
- 实时语言纠错

### 📚 闯关式单词学习
- 渐进式词汇学习
- 互动式记忆训练
- 学习进度跟踪

## 🏗️ 技术架构

### 前端 (HTML/CSS/JavaScript)
```
web/
├── index.html          # 主页面
├── css/
│   ├── main.css       # 主样式
│   └── components.css # 组件样式
└── js/
    ├── main.js        # 主逻辑
    ├── api.js         # API调用
    └── components.js  # UI组件
```

### 后端 (Flask API)
```
api/
├── app.py             # Flask应用
└── routes/            # API路由 (可扩展)
```

### 智能体核心 (保留原有)
```
agents/
├── agent_base.py      # 基础代理类
├── scenario_agent.py  # 场景对话代理
├── conversation_agent.py # 自由对话代理
├── vocab_agent.py     # 词汇学习代理
└── session_history.py # 会话管理
```

## 🔧 配置说明

### 方式1: Web界面配置
1. 点击右上角"设置"按钮
2. 输入OpenAI API Key和Base URL
3. 点击"保存"

### 方式2: 环境变量配置
创建 `.env` 文件并配置：
```env
OPENAI_API_KEY=sk-...
OPENAI_API_BASE=https://api.openai.com/v1
```

## 📱 响应式设计

- 🖥️ 桌面端优化体验
- 📱 移动端自适应布局
- ⌨️ 键盘快捷键支持
- 🎨 现代化UI设计

## 🔄 与原版对比

| 特性 | 原版 (Gradio) | HTML版本 |
|------|---------------|----------|
| 界面框架 | Gradio | HTML/CSS/JS |
| 智能体核心 | ✅ 完整保留 | ✅ 完整保留 |
| 场景对话 | ✅ | ✅ |
| 自由对话 | ✅ | ✅ |
| 词汇学习 | ✅ | ✅ |
| 响应式设计 | ❌ | ✅ |
| 自定义样式 | 有限 | ✅ 完全自定义 |
| 部署灵活性 | 中等 | ✅ 高 |

## 🛠️ 开发指南

### 添加新场景
1. 在 `prompts/` 添加提示词文件
2. 在 `content/intro/` 添加初始对话
3. 在 `content/page/` 添加场景介绍
4. 更新前端场景选项

### 自定义样式
- 修改 `web/css/main.css` 调整主题
- 修改 `web/css/components.css` 调整组件样式

### 扩展API
- 在 `api/app.py` 添加新的路由
- 在 `web/js/api.js` 添加对应的API调用方法

## 🐛 故障排除

### 常见问题

**Q: 无法连接到后端服务**
- 检查端口5000是否被占用
- 确认依赖项已正确安装

**Q: API调用失败**
- 检查OpenAI API Key配置
- 确认网络连接正常

**Q: 页面样式异常**
- 清除浏览器缓存
- 检查CSS文件是否正确加载

### 日志查看
应用日志会输出到控制台和 `logs/app.log` 文件

## 📄 许可证

本项目保持与原版相同的许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

---

**享受学习英语的乐趣！** 🎉
