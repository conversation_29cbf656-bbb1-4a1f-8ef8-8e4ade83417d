// 主应用类
class LanguageMentorApp {
    constructor() {
        this.currentTab = 'scenario';
        this.currentScenario = null;
        this.isInitialized = false;
        this.speakingPractice = null;

        this.init();
    }

    async init() {
        try {
            // 检查API连接
            await this.checkAPIConnection();
            
            // 初始化事件监听器
            this.initEventListeners();
            
            // 加载设置
            this.loadSettings();
            
            // 加载词汇学习介绍
            await this.loadVocabIntro();

            // 初始化口语练习模块
            this.initSpeakingPractice();

            this.isInitialized = true;
            console.log('LanguageMentor 应用初始化完成');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            ErrorHandler.show('应用初始化失败，请检查后端服务是否正常运行');
        }
    }

    async checkAPIConnection() {
        try {
            await api.healthCheck();
            console.log('API 连接正常');
        } catch (error) {
            throw new Error('无法连接到后端服务');
        }
    }

    initEventListeners() {
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.currentTarget.dataset.tab;
                this.switchTab(tabId);
            });
        });

        // 场景选择
        document.querySelectorAll('input[name="scenario"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.handleScenarioChange(e.target.value);
            });
        });

        // 聊天输入框回车发送
        this.initChatInputs();

        // 设置相关
        this.initSettingsModal();

        // 词汇学习下一关按钮
        const nextRoundBtn = document.getElementById('nextRoundBtn');
        if (nextRoundBtn) {
            nextRoundBtn.addEventListener('click', () => {
                this.startNextVocabRound();
            });
        }
    }

    initChatInputs() {
        // 场景聊天
        const scenarioInput = document.getElementById('scenarioInput');
        const scenarioSendBtn = document.getElementById('scenarioSendBtn');
        
        if (scenarioInput && scenarioSendBtn) {
            scenarioInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !scenarioSendBtn.disabled) {
                    this.sendScenarioMessage();
                }
            });
            
            scenarioSendBtn.addEventListener('click', () => {
                this.sendScenarioMessage();
            });
        }

        // 自由对话
        const conversationInput = document.getElementById('conversationInput');
        const conversationSendBtn = document.getElementById('conversationSendBtn');
        
        if (conversationInput && conversationSendBtn) {
            conversationInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendConversationMessage();
                }
            });
            
            conversationSendBtn.addEventListener('click', () => {
                this.sendConversationMessage();
            });
        }

        // 词汇学习
        const vocabInput = document.getElementById('vocabInput');
        const vocabSendBtn = document.getElementById('vocabSendBtn');
        
        if (vocabInput && vocabSendBtn) {
            vocabInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendVocabMessage();
                }
            });
            
            vocabSendBtn.addEventListener('click', () => {
                this.sendVocabMessage();
            });
        }
    }

    initSettingsModal() {
        const settingsBtn = document.getElementById('settingsBtn');
        const closeSettingsBtn = document.getElementById('closeSettingsBtn');
        const cancelSettingsBtn = document.getElementById('cancelSettingsBtn');
        const saveSettingsBtn = document.getElementById('saveSettingsBtn');
        const settingsModal = document.getElementById('settingsModal');

        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                UIComponents.applySettingsToForm();
                UIComponents.showModal('settingsModal');
            });
        }

        if (closeSettingsBtn) {
            closeSettingsBtn.addEventListener('click', () => {
                UIComponents.hideModal('settingsModal');
            });
        }

        if (cancelSettingsBtn) {
            cancelSettingsBtn.addEventListener('click', () => {
                UIComponents.hideModal('settingsModal');
            });
        }

        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        // 点击模态框外部关闭
        if (settingsModal) {
            settingsModal.addEventListener('click', (e) => {
                if (e.target === settingsModal) {
                    UIComponents.hideModal('settingsModal');
                }
            });
        }
    }

    switchTab(tabId) {
        this.currentTab = tabId;
        UIComponents.switchTab(tabId);
        
        // 根据标签页执行特定初始化
        if (tabId === 'scenario') {
            // 场景标签页无需特殊处理
        } else if (tabId === 'conversation') {
            // 自由对话标签页
            UIComponents.setInputState('conversationInput', 'conversationSendBtn', true);
        } else if (tabId === 'speaking') {
            // 口语练习标签页
            UIComponents.setInputState('speakingInput', 'speakingSendBtn', true);
        } else if (tabId === 'vocab') {
            // 词汇学习标签页
            UIComponents.setInputState('vocabInput', 'vocabSendBtn', true);
        }
    }

    async handleScenarioChange(scenarioId) {
        try {
            this.currentScenario = scenarioId;
            LoadingManager.show('加载场景信息...');

            // 获取场景介绍
            const introResponse = await api.getScenarioIntro(scenarioId);
            if (introResponse.success) {
                UIComponents.updateScenarioIntro(introResponse.data.intro);
            }

            // 开始场景对话
            const sessionId = sessionManager.getSessionId('scenario', scenarioId);
            const startResponse = await api.startScenario(scenarioId, sessionId);
            
            if (startResponse.success) {
                // 清空聊天记录并显示初始消息
                UIComponents.clearMessages('scenarioMessages');
                UIComponents.addMessage('scenarioMessages', startResponse.data.message, false);
                
                // 启用输入框
                UIComponents.setInputState('scenarioInput', 'scenarioSendBtn', true);
            }

        } catch (error) {
            console.error('场景切换失败:', error);
            ErrorHandler.show('场景加载失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    async sendScenarioMessage() {
        const input = document.getElementById('scenarioInput');
        const message = input.value.trim();
        
        if (!message || !this.currentScenario) return;

        try {
            // 添加用户消息
            UIComponents.addMessage('scenarioMessages', message, true);
            input.value = '';
            
            // 禁用输入框
            UIComponents.setInputState('scenarioInput', 'scenarioSendBtn', false);
            
            // 发送消息到API
            const sessionId = sessionManager.getSessionId('scenario', this.currentScenario);
            const response = await api.scenarioChat(this.currentScenario, message, sessionId);
            
            if (response.success) {
                // 添加机器人回复
                await UIComponents.typeMessage('scenarioMessages', response.data.message, false, 30);
            }
            
        } catch (error) {
            console.error('发送场景消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('scenarioInput', 'scenarioSendBtn', true);
        }
    }

    async sendConversationMessage() {
        const input = document.getElementById('conversationInput');
        const message = input.value.trim();
        
        if (!message) return;

        try {
            // 添加用户消息
            UIComponents.addMessage('conversationMessages', message, true);
            input.value = '';
            
            // 禁用输入框
            UIComponents.setInputState('conversationInput', 'conversationSendBtn', false);
            
            // 发送消息到API
            const sessionId = sessionManager.getSessionId('conversation');
            const response = await api.conversationChat(message, sessionId);
            
            if (response.success) {
                // 添加机器人回复
                await UIComponents.typeMessage('conversationMessages', response.data.message, false, 30);
            }
            
        } catch (error) {
            console.error('发送对话消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('conversationInput', 'conversationSendBtn', true);
        }
    }

    async sendVocabMessage() {
        const input = document.getElementById('vocabInput');
        const message = input.value.trim();
        
        if (!message) return;

        try {
            // 添加用户消息
            UIComponents.addMessage('vocabMessages', message, true);
            input.value = '';
            
            // 禁用输入框
            UIComponents.setInputState('vocabInput', 'vocabSendBtn', false);
            
            // 发送消息到API
            const sessionId = sessionManager.getSessionId('vocab');
            const response = await api.vocabChat(message, sessionId);
            
            if (response.success) {
                // 添加机器人回复
                await UIComponents.typeMessage('vocabMessages', response.data.message, false, 30);
            }
            
        } catch (error) {
            console.error('发送词汇消息失败:', error);
            ErrorHandler.show('发送消息失败: ' + error.message);
        } finally {
            // 重新启用输入框
            UIComponents.setInputState('vocabInput', 'vocabSendBtn', true);
        }
    }

    async startNextVocabRound() {
        try {
            LoadingManager.show('开始新一关...');
            
            // 重置会话
            sessionManager.resetSession('vocab');
            const sessionId = sessionManager.getSessionId('vocab');
            
            // 开始新的词汇学习
            const response = await api.startVocab(sessionId);
            
            if (response.success) {
                // 清空聊天记录并显示新的对话
                UIComponents.clearMessages('vocabMessages');
                UIComponents.addMessage('vocabMessages', response.data.user_message, true);
                await UIComponents.typeMessage('vocabMessages', response.data.bot_message, false, 30);
            }
            
        } catch (error) {
            console.error('开始新一关失败:', error);
            ErrorHandler.show('开始新一关失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    async loadVocabIntro() {
        try {
            const response = await api.getVocabIntro();
            if (response.success) {
                UIComponents.updateVocabIntro(response.data.intro);
            }
        } catch (error) {
            console.error('加载词汇学习介绍失败:', error);
        }
    }

    loadSettings() {
        UIComponents.applySettingsToForm();
    }

    async saveSettings() {
        const apiKey = document.getElementById('apiKey').value.trim();
        
        const formData = { apiKey };
        const validation = UIComponents.validateForm(formData);
        
        if (!validation.isValid) {
            ErrorHandler.show(validation.errors.join('\n'));
            return;
        }

        try {
            LoadingManager.show('保存设置中...');
            
            // 保存到本地存储
            UIComponents.saveSettings(formData);
            
            // 更新后端配置
            if (apiKey) {
                await api.updateConfig(apiKey);
            }
            
            UIComponents.hideModal('settingsModal');
            ErrorHandler.showSuccess('设置保存成功');
            
        } catch (error) {
            console.error('保存设置失败:', error);
            ErrorHandler.show('保存设置失败: ' + error.message);
        } finally {
            LoadingManager.hide();
        }
    }

    initSpeakingPractice() {
        // 初始化口语练习模块
        if (typeof SpeakingPractice !== 'undefined') {
            this.speakingPractice = new SpeakingPractice();
            console.log('口语练习模块初始化完成');
        } else {
            console.warn('口语练习模块未加载');
        }
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LanguageMentorApp();
});
