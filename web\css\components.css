/* 场景选择器样式 */
.scenario-selector {
    margin-bottom: 2rem;
}

.scenario-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.scenario-option {
    cursor: pointer;
}

.scenario-option input[type="radio"] {
    display: none;
}

.scenario-card {
    padding: 2rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.scenario-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.scenario-option input[type="radio"]:checked + .scenario-card {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.scenario-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.scenario-option input[type="radio"]:checked + .scenario-card i {
    color: white;
}

.scenario-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.scenario-card p {
    color: #64748b;
    font-size: 0.9rem;
}

.scenario-option input[type="radio"]:checked + .scenario-card p {
    color: rgba(255, 255, 255, 0.9);
}

/* 场景介绍样式 */
.scenario-intro {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: #4a5568;
    line-height: 1.6;
}

.vocab-intro {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: #4a5568;
    line-height: 1.6;
}

/* 聊天容器样式 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 500px;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    background: white;
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.welcome-message {
    text-align: center;
    color: #64748b;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 2px dashed #e2e8f0;
}

/* 消息样式 */
.message {
    display: flex;
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease;
}

.message.user {
    justify-content: flex-end;
}

.message.bot {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 1rem 1.5rem;
    border-radius: 18px;
    word-wrap: break-word;
    line-height: 1.5;
}

.message.user .message-content {
    background: #667eea;
    color: white;
    border-bottom-right-radius: 6px;
}

.message.bot .message-content {
    background: #f1f5f9;
    color: #2d3748;
    border-bottom-left-radius: 6px;
}

.message-time {
    font-size: 0.75rem;
    color: #a0aec0;
    margin-top: 0.25rem;
    text-align: right;
}

.message.bot .message-time {
    text-align: left;
}

/* 输入区域样式 */
.chat-input-container {
    border-top: 1px solid #e2e8f0;
    padding: 1rem;
    background: #f8fafc;
}

.chat-input-wrapper {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.chat-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 24px;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
    background: white;
}

.chat-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.chat-input:disabled {
    background: #f1f5f9;
    color: #a0aec0;
    cursor: not-allowed;
}

.send-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: #667eea;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover:not(:disabled) {
    background: #5a67d8;
    transform: scale(1.05);
}

.send-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #a0aec0;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    color: #667eea;
    background: #f7fafc;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* 设置表单样式 */
.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4a5568;
}

.setting-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.setting-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .scenario-options {
        grid-template-columns: 1fr;
    }
    
    .chat-container {
        height: 400px;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
}

/* 口语练习样式 */
.speaking-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.topic-selector {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.topic-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.topic-card {
    padding: 1rem;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.topic-card:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.topic-card.selected {
    border-color: #3b82f6;
    background: #eff6ff;
}

.current-topic {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #eff6ff;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.topic-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.topic-label {
    font-weight: 600;
    color: #374151;
}

.topic-name {
    color: #3b82f6;
    font-weight: 500;
}

.speaking-input-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.voice-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.voice-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background: #3b82f6;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.voice-btn:hover {
    background: #2563eb;
    transform: scale(1.05);
}

.voice-btn:active {
    transform: scale(0.95);
}

.voice-btn.recording {
    background: #ef4444;
    animation: pulse 1.5s infinite;
}

.voice-btn.playing {
    background: #10b981;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

.voice-status {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem;
    background: #fef2f2;
    border-radius: 8px;
    border: 1px solid #fecaca;
}

.recording-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #dc2626;
    font-weight: 500;
}

.recording-dot {
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}
